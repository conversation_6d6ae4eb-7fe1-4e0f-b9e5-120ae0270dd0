import type React from 'react';
import '@/app/globals.css';
import Header from '@/components/Header';
//import { Inter } from 'next/font/google';

//const inter = Inter({ subsets: ['latin'] });

<<<<<<< HEAD
export const metadata: Metadata = {
  title: 'Revoland - Nền Tảng Công Nghệ Bất Động Sản Toàn Diện',
  description:
    'Revoland - Giải pháp bất động sản toàn diện, đáng tin cậy. Chuyên cung cấp dịch vụ mua bán, cho thuê nhà đất, biệt thự, căn hộ và đất nền với đội ngũ chuyên gia uy tín, tận tâm.',
};
=======
// export const metadata: Metadata = {
//   title: 'RevoLand - Giải Pháp Toàn Diện Cho Mọi <PERSON>hu <PERSON>t Động Sản',
//   description:
//     'RevoLand - Gi<PERSON><PERSON> pháp bất động sản toàn di<PERSON>, đ<PERSON>g tin cậy. <PERSON><PERSON>ên cung cấp dịch vụ mua bán, cho thu<PERSON> nhà đất, biệt thự, căn hộ và đất nền với đội ngũ chuyên gia uy tín, tận tâm.',
// };
>>>>>>> dev

export default function PropertiesLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="font-mann">
      <Header />
      {children}
    </div>
  );
}
